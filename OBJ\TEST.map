Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to dji_can_motor.o(i.CAN1_RX0_IRQHandler) for CAN1_RX0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to control.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to timer.o(i.TIM5_IRQHandler) for TIM5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to dbus_dr16.o(i.UART4_IRQHandler) for UART4_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    motor_move.o(i.Motor_Back) refers to motor_move.o(i.Motor_move) for Motor_move
    motor_move.o(i.Motor_Back) refers to motor_move.o(.data) for z
    motor_move.o(i.Motor_Back1) refers to motor_move.o(i.Motor_move) for Motor_move
    motor_move.o(i.Motor_Back1) refers to motor_move.o(.data) for s
    motor_move.o(i.Motor_Back2) refers to motor_move.o(i.Motor_move) for Motor_move
    motor_move.o(i.Motor_Back2) refers to motor_move.o(.data) for m
    motor_move.o(i.Motor_Back4) refers to motor_move.o(i.Motor_move) for Motor_move
    motor_move.o(i.Motor_Back4) refers to motor_move.o(.data) for r
    motor_move.o(i.Motor_Left) refers to motor_move.o(i.Motor_walk2) for Motor_walk2
    motor_move.o(i.Motor_Left1) refers to motor_move.o(i.Motor_walk2) for Motor_walk2
    motor_move.o(i.Motor_Left2) refers to motor_move.o(i.Motor_walk2) for Motor_walk2
    motor_move.o(i.Motor_Right) refers to motor_move.o(i.Motor_walk2) for Motor_walk2
    motor_move.o(i.Motor_Right1) refers to motor_move.o(i.Motor_walk2) for Motor_walk2
    motor_move.o(i.Motor_Right2) refers to motor_move.o(i.Motor_walk2) for Motor_walk2
    motor_move.o(i.Motor_Walk) refers to motor_move.o(i.Motor_move) for Motor_move
    motor_move.o(i.Motor_Walk) refers to motor_move.o(.data) for z
    motor_move.o(i.Motor_Walk1) refers to motor_move.o(i.Motor_move) for Motor_move
    motor_move.o(i.Motor_Walk1) refers to motor_move.o(.data) for s
    motor_move.o(i.Motor_Walk2) refers to motor_move.o(i.Motor_move) for Motor_move
    motor_move.o(i.Motor_Walk2) refers to motor_move.o(.data) for m
    motor_move.o(i.Motor_Walk3) refers to motor_move.o(i.Motor_move) for Motor_move
    motor_move.o(i.Motor_Walk3) refers to motor_move.o(.data) for z
    motor_move.o(i.Motor_Walk4) refers to motor_move.o(i.Motor_move) for Motor_move
    motor_move.o(i.Motor_Walk4) refers to motor_move.o(.data) for r
    motor_move.o(i.Motor_Walka) refers to motor_move.o(i.Motor_move) for Motor_move
    motor_move.o(i.Motor_Walka) refers to motor_move.o(.data) for s
    motor_move.o(i.Motor_YD) refers to motor_move.o(i.Motor_move) for Motor_move
    motor_move.o(i.Motor_YD) refers to motor_move.o(.data) for s
    motor_move.o(i.Motor_ctrl) refers to motor_move.o(i.Motor_move) for Motor_move
    motor_move.o(i.Motor_ctrl) refers to motor_move.o(.data) for r
    motor_move.o(i.Motor_move) refers to dji_can_motor.o(i.Can_Sevo_Control) for Can_Sevo_Control
    motor_move.o(i.Motor_move) refers to delay.o(i.delay_ms) for delay_ms
    motor_move.o(i.Motor_move) refers to motor_move.o(.data) for i
    motor_move.o(i.Motor_stand) refers to motor_move.o(i.Motor_move) for Motor_move
    motor_move.o(i.Motor_stand) refers to motor_move.o(.data) for z
    motor_move.o(i.Motor_stand1) refers to motor_move.o(i.Motor_move) for Motor_move
    motor_move.o(i.Motor_stand1) refers to motor_move.o(.data) for s
    motor_move.o(i.Motor_walk2) refers to dji_can_motor.o(i.Can_Sevo_Control) for Can_Sevo_Control
    motor_move.o(i.Motor_walk2) refers to delay.o(i.delay_ms) for delay_ms
    motor_move.o(i.Motor_walk2) refers to motor_move.o(.data) for i
    test.o(i.main) refers to sys.o(i.Stm32_Clock_Init) for Stm32_Clock_Init
    test.o(i.main) refers to delay.o(i.delay_init) for delay_init
    test.o(i.main) refers to usart.o(i.uart_init) for uart_init
    test.o(i.main) refers to data_transfer.o(i.DMAUART_Send_Config) for DMAUART_Send_Config
    test.o(i.main) refers to data_transfer.o(i.DMAUART_Enable) for DMAUART_Enable
    test.o(i.main) refers to dbus_dr16.o(i.Dbus_DR16_Init) for Dbus_DR16_Init
    test.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    test.o(i.main) refers to beep.o(i.BEEP_Init) for BEEP_Init
    test.o(i.main) refers to dji_can_motor.o(i.CAN1_Motor_Init) for CAN1_Motor_Init
    test.o(i.main) refers to pwm_motor.o(i.PWM1_Motor_Init) for PWM1_Motor_Init
    test.o(i.main) refers to pwm_motor.o(i.PWM2_Motor_Init) for PWM2_Motor_Init
    test.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    test.o(i.main) refers to bmi088.o(i.BMI088_initial) for BMI088_initial
    test.o(i.main) refers to timer.o(i.TIM3_Int_Init) for TIM3_Int_Init
    test.o(i.main) refers to motor_move.o(i.Motor_Walk3) for Motor_Walk3
    test.o(i.main) refers to motor_move.o(i.Motor_Back) for Motor_Back
    test.o(i.main) refers to motor_move.o(i.Motor_Left) for Motor_Left
    test.o(i.main) refers to motor_move.o(i.Motor_Right) for Motor_Right
    test.o(i.main) refers to motor_move.o(i.Motor_stand) for Motor_stand
    test.o(i.main) refers to motor_move.o(i.Motor_Walk) for Motor_Walk
    test.o(i.main) refers to motor_move.o(i.Motor_Left2) for Motor_Left2
    test.o(i.main) refers to data_transfer.o(.bss) for data_to_send
    test.o(i.main) refers to dbus_dr16.o(.bss) for RC_CtrlData
    test.o(i.main) refers to control.o(.data) for ARMED
    test.o(i.main) refers to beep.o(.data) for BEEP_state
    test.o(i.main) refers to test.o(.data) for change
    test.o(i.main) refers to motor_move.o(.data) for z
    test.o(i.main) refers to motor_move.o(i.Motor_Right2) for Motor_Right2
    test.o(i.main) refers to motor_move.o(i.Motor_Walk1) for Motor_Walk1
    test.o(i.main) refers to motor_move.o(i.Motor_Back1) for Motor_Back1
    test.o(i.main) refers to motor_move.o(i.Motor_Left1) for Motor_Left1
    test.o(i.main) refers to motor_move.o(i.Motor_Right1) for Motor_Right1
    test.o(i.main) refers to motor_move.o(i.Motor_stand1) for Motor_stand1
    test.o(i.main) refers to motor_move.o(i.Motor_Walk4) for Motor_Walk4
    test.o(i.main) refers to motor_move.o(i.Motor_Back2) for Motor_Back2
    test.o(i.main) refers to motor_move.o(i.Motor_ctrl) for Motor_ctrl
    dji_can_motor.o(i.CAN1_Motor_Init) refers to can.o(i.CAN1_Mode_Init) for CAN1_Mode_Init
    dji_can_motor.o(i.CAN1_RX0_IRQHandler) refers to can.o(i.CAN1_Rx_Msg) for CAN1_Rx_Msg
    dji_can_motor.o(i.Can_Sevo_Control) refers to can.o(i.CAN1_Tx_Staus) for CAN1_Tx_Staus
    dji_can_motor.o(i.Can_Sevo_Control) refers to can.o(i.CAN1_Tx_Msg) for CAN1_Tx_Msg
    dji_can_motor.o(i.Can_Sevo_Control) refers to dji_can_motor.o(.data) for CAN_SendBuf
    control.o(i.TIM3_IRQHandler) refers to bmi088.o(i.BMI088_Callback) for BMI088_Callback
    control.o(i.TIM3_IRQHandler) refers to control.o(.data) for Tim3_Flag
    control.o(i.TIM3_IRQHandler) refers to dbus_dr16.o(.data) for RC_OffLine
    bluetooth.o(i.BlueTooth_Receive) refers to bluetooth.o(.bss) for Blue_dat
    bluetooth.o(i.BlueTooth_Receive) refers to bluetooth.o(.data) for BlueCK
    pwm_motor.o(i.PWM1_Motor_Init) refers to sys.o(i.GPIO_Set) for GPIO_Set
    pwm_motor.o(i.PWM1_Motor_Init) refers to sys.o(i.GPIO_AF_Set) for GPIO_AF_Set
    pwm_motor.o(i.PWM2_Motor_Init) refers to sys.o(i.GPIO_Set) for GPIO_Set
    pwm_motor.o(i.PWM2_Motor_Init) refers to sys.o(i.GPIO_AF_Set) for GPIO_AF_Set
    dbus_dr16.o(i.Dbus_DR16_Init) refers to dbus_dr16.o(i.Dbus_DMA_Config) for Dbus_DMA_Config
    dbus_dr16.o(i.Dbus_DR16_Init) refers to dbus_dr16.o(i.uart4_init) for uart4_init
    dbus_dr16.o(i.Dbus_DR16_Init) refers to dbus_dr16.o(.bss) for Dbus_buf
    dbus_dr16.o(i.RemoteDataProcess) refers to dbus_dr16.o(.bss) for RC_CtrlData
    dbus_dr16.o(i.UART4_IRQHandler) refers to dbus_dr16.o(i.RemoteDataProcess) for RemoteDataProcess
    dbus_dr16.o(i.UART4_IRQHandler) refers to dbus_dr16.o(.data) for RC_OffLine
    dbus_dr16.o(i.UART4_IRQHandler) refers to dbus_dr16.o(.bss) for Dbus_buf
    dbus_dr16.o(i.uart4_init) refers to sys.o(i.GPIO_Set) for GPIO_Set
    dbus_dr16.o(i.uart4_init) refers to sys.o(i.GPIO_AF_Set) for GPIO_AF_Set
    dbus_dr16.o(i.uart4_init) refers to sys.o(i.MY_NVIC_Init) for MY_NVIC_Init
    data_transfer.o(i.Data_Send_Attitude) refers to data_transfer.o(i.DMAUART_Enable) for DMAUART_Enable
    data_transfer.o(i.Data_Send_Attitude) refers to data_transfer.o(.bss) for data_to_send
    data_transfer.o(i.Data_Send_Attitude) refers to bmi088.o(.data) for Pitch
    data_transfer.o(i.Data_Send_Attitude) refers to control.o(.data) for ARMED
    delay.o(i.delay_init) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(i.delay_xms) for delay_xms
    delay.o(i.delay_us) refers to delay.o(.data) for fac_us
    delay.o(i.delay_xms) refers to delay.o(.data) for fac_ms
    sys.o(i.MY_NVIC_Init) refers to sys.o(i.MY_NVIC_PriorityGroupConfig) for MY_NVIC_PriorityGroupConfig
    sys.o(i.Stm32_Clock_Init) refers to sys.o(i.Sys_Clock_Set) for Sys_Clock_Set
    sys.o(i.Stm32_Clock_Init) refers to sys.o(i.MY_NVIC_SetVectorTable) for MY_NVIC_SetVectorTable
    sys.o(i.Sys_Standby) refers to sys.o(i.WFI_SET) for WFI_SET
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.Usart1_SendChar) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.Usart2_SendChar) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers to sys.o(i.GPIO_Set) for GPIO_Set
    usart.o(i.uart_init) refers to sys.o(i.GPIO_AF_Set) for GPIO_AF_Set
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    key.o(i.KEY_Init) refers to sys.o(i.GPIO_Set) for GPIO_Set
    key.o(i.KEY_Scan) refers to key.o(.data) for KEY_Times
    timer.o(i.TIM14_PWM_Init) refers to sys.o(i.GPIO_Set) for GPIO_Set
    timer.o(i.TIM14_PWM_Init) refers to sys.o(i.GPIO_AF_Set) for GPIO_AF_Set
    timer.o(i.TIM3_Int_Init) refers to sys.o(i.MY_NVIC_Init) for MY_NVIC_Init
    timer.o(i.TIM5_CH1_Cap_Init) refers to sys.o(i.GPIO_Set) for GPIO_Set
    timer.o(i.TIM5_CH1_Cap_Init) refers to sys.o(i.GPIO_AF_Set) for GPIO_AF_Set
    timer.o(i.TIM5_CH1_Cap_Init) refers to sys.o(i.MY_NVIC_Init) for MY_NVIC_Init
    timer.o(i.TIM5_IRQHandler) refers to timer.o(.data) for TIM5CH1_CAPTURE_STA
    timer.o(i.TIM9_CH2_PWM_Init) refers to sys.o(i.GPIO_Set) for GPIO_Set
    timer.o(i.TIM9_CH2_PWM_Init) refers to sys.o(i.GPIO_AF_Set) for GPIO_AF_Set
    led.o(i.LED_Control) refers to led.o(i.Led_Flash1) for Led_Flash1
    led.o(i.LED_Control) refers to led.o(i.Led_Flash2) for Led_Flash2
    led.o(i.LED_Control) refers to led.o(i.Led_Flash3) for Led_Flash3
    led.o(i.LED_Control) refers to led.o(.data) for LED_state
    led.o(i.LED_Init) refers to sys.o(i.GPIO_Set) for GPIO_Set
    led.o(i.Led_Flash1) refers to led.o(.data) for LED_timetick
    led.o(i.Led_Flash2) refers to led.o(.data) for LED_timetick
    led.o(i.Led_Flash3) refers to led.o(.data) for LED_timetick
    can.o(i.CAN1_Mode_Init) refers to sys.o(i.GPIO_Set) for GPIO_Set
    can.o(i.CAN1_Mode_Init) refers to sys.o(i.GPIO_AF_Set) for GPIO_AF_Set
    can.o(i.CAN1_Mode_Init) refers to sys.o(i.MY_NVIC_Init) for MY_NVIC_Init
    can.o(i.CAN1_Receive_Msg) refers to can.o(i.CAN1_Msg_Pend) for CAN1_Msg_Pend
    can.o(i.CAN1_Receive_Msg) refers to can.o(i.CAN1_Rx_Msg) for CAN1_Rx_Msg
    can.o(i.CAN1_Send_Msg) refers to can.o(i.CAN1_Tx_Msg) for CAN1_Tx_Msg
    can.o(i.CAN1_Send_Msg) refers to can.o(i.CAN1_Tx_Staus) for CAN1_Tx_Staus
    beep.o(i.BEEP_Control) refers to beep.o(i.BEEP_Flash1) for BEEP_Flash1
    beep.o(i.BEEP_Control) refers to beep.o(i.BEEP_Flash2) for BEEP_Flash2
    beep.o(i.BEEP_Control) refers to beep.o(i.BEEP_Flash3) for BEEP_Flash3
    beep.o(i.BEEP_Control) refers to beep.o(.data) for BEEP_state
    beep.o(i.BEEP_Flash1) refers to beep.o(.data) for BEEP_timetick
    beep.o(i.BEEP_Flash2) refers to beep.o(.data) for BEEP_timetick
    beep.o(i.BEEP_Flash3) refers to beep.o(.data) for BEEP_timetick
    beep.o(i.BEEP_Init) refers to sys.o(i.GPIO_Set) for GPIO_Set
    beep.o(i.BEEP_Init) refers to sys.o(i.GPIO_AF_Set) for GPIO_AF_Set
    beep.o(i.BEEP_ShortRing) refers to beep.o(.data) for BEEP_timetick
    stmflash.o(i.STMFLASH_EraseSector) refers to stmflash.o(i.STMFLASH_WaitDone) for STMFLASH_WaitDone
    stmflash.o(i.STMFLASH_Read) refers to stmflash.o(i.STMFLASH_ReadWord) for STMFLASH_ReadWord
    stmflash.o(i.STMFLASH_WaitDone) refers to stmflash.o(i.STMFLASH_GetStatus) for STMFLASH_GetStatus
    stmflash.o(i.STMFLASH_WaitDone) refers to delay.o(i.delay_us) for delay_us
    stmflash.o(i.STMFLASH_Write) refers to stmflash.o(i.STMFLASH_Unlock) for STMFLASH_Unlock
    stmflash.o(i.STMFLASH_Write) refers to stmflash.o(i.STMFLASH_ReadWord) for STMFLASH_ReadWord
    stmflash.o(i.STMFLASH_Write) refers to stmflash.o(i.STMFLASH_GetFlashSector) for STMFLASH_GetFlashSector
    stmflash.o(i.STMFLASH_Write) refers to stmflash.o(i.STMFLASH_EraseSector) for STMFLASH_EraseSector
    stmflash.o(i.STMFLASH_Write) refers to stmflash.o(i.STMFLASH_WriteWord) for STMFLASH_WriteWord
    stmflash.o(i.STMFLASH_Write) refers to stmflash.o(i.STMFLASH_Lock) for STMFLASH_Lock
    stmflash.o(i.STMFLASH_WriteWord) refers to stmflash.o(i.STMFLASH_WaitDone) for STMFLASH_WaitDone
    bmi088.o(i.BMI088_Callback) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    bmi088.o(i.BMI088_Callback) refers to mahonyahrs.o(i.MahonyAHRSupdateIMU) for MahonyAHRSupdateIMU
    bmi088.o(i.BMI088_Callback) refers to mahonyahrs.o(i.get_angle) for get_angle
    bmi088.o(i.BMI088_Callback) refers to bmi088.o(.data) for BMI088_ACCEL_SEN
    bmi088.o(i.BMI088_Callback) refers to bmi088.o(.bss) for BMI088_accelerometer
    bmi088.o(i.BMI088_initial) refers to sys.o(i.GPIO_Set) for GPIO_Set
    bmi088.o(i.BMI088_initial) refers to spi.o(i.SPI1_Init) for SPI1_Init
    bmi088.o(i.BMI088_initial) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    bmi088.o(i.BMI088_initial) refers to delay.o(i.delay_ms) for delay_ms
    spi.o(i.SPI1_Init) refers to sys.o(i.GPIO_Set) for GPIO_Set
    spi.o(i.SPI1_Init) refers to sys.o(i.GPIO_AF_Set) for GPIO_AF_Set
    spi.o(i.SPI1_Init) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    mahonyahrs.o(i.MahonyAHRSupdateIMU) refers to mahonyahrs.o(i.invSqrt) for invSqrt
    mahonyahrs.o(i.MahonyAHRSupdateIMU) refers to mahonyahrs.o(.data) for twoKi
    mahonyahrs.o(i.get_angle) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    mahonyahrs.o(i.get_angle) refers to asinf.o(i.__hardfp_asinf) for __hardfp_asinf
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    asinf.o(i.__hardfp_asinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf.o(i.__hardfp_asinf) refers to sqrtf.o(i.sqrtf) for sqrtf
    asinf.o(i.__hardfp_asinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    asinf.o(i.__hardfp_asinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    asinf.o(i.__hardfp_asinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    asinf.o(i.__hardfp_asinf) refers to _rserrno.o(.text) for __set_errno
    asinf.o(i.__hardfp_asinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    asinf.o(i.__softfp_asinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf.o(i.__softfp_asinf) refers to asinf.o(i.__hardfp_asinf) for __hardfp_asinf
    asinf.o(i.asinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf.o(i.asinf) refers to asinf.o(i.__hardfp_asinf) for __hardfp_asinf
    asinf_x.o(i.____hardfp_asinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf_x.o(i.____hardfp_asinf$lsc) refers to sqrtf.o(i.sqrtf) for sqrtf
    asinf_x.o(i.____hardfp_asinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    asinf_x.o(i.____hardfp_asinf$lsc) refers to _rserrno.o(.text) for __set_errno
    asinf_x.o(i.____softfp_asinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf_x.o(i.____softfp_asinf$lsc) refers to asinf_x.o(i.____hardfp_asinf$lsc) for ____hardfp_asinf$lsc
    asinf_x.o(i.__asinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf_x.o(i.__asinf$lsc) refers to asinf_x.o(i.____hardfp_asinf$lsc) for ____hardfp_asinf$lsc
    atan2f.o(i.__hardfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__hardfp_atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f.o(i.__softfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f.o(i.atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to test.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing motor_move.o(.rev16_text), (4 bytes).
    Removing motor_move.o(.revsh_text), (4 bytes).
    Removing motor_move.o(i.Motor_Back4), (132 bytes).
    Removing motor_move.o(i.Motor_Walk2), (132 bytes).
    Removing motor_move.o(i.Motor_Walka), (104 bytes).
    Removing motor_move.o(i.Motor_YD), (120 bytes).
    Removing test.o(.rev16_text), (4 bytes).
    Removing test.o(.revsh_text), (4 bytes).
    Removing dji_can_motor.o(.rev16_text), (4 bytes).
    Removing dji_can_motor.o(.revsh_text), (4 bytes).
    Removing control.o(.rev16_text), (4 bytes).
    Removing control.o(.revsh_text), (4 bytes).
    Removing bluetooth.o(.rev16_text), (4 bytes).
    Removing bluetooth.o(.revsh_text), (4 bytes).
    Removing bluetooth.o(i.BlueTooth_Receive), (312 bytes).
    Removing bluetooth.o(.bss), (16 bytes).
    Removing bluetooth.o(.data), (10 bytes).
    Removing pwm_motor.o(.rev16_text), (4 bytes).
    Removing pwm_motor.o(.revsh_text), (4 bytes).
    Removing dbus_dr16.o(.rev16_text), (4 bytes).
    Removing dbus_dr16.o(.revsh_text), (4 bytes).
    Removing data_transfer.o(.rev16_text), (4 bytes).
    Removing data_transfer.o(.revsh_text), (4 bytes).
    Removing data_transfer.o(i.Data_Send_Attitude), (340 bytes).
    Removing data_transfer.o(.data), (9 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(i.delay_us), (60 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(i.Ex_NVIC_Config), (180 bytes).
    Removing sys.o(i.INTX_DISABLE), (4 bytes).
    Removing sys.o(i.INTX_ENABLE), (4 bytes).
    Removing sys.o(i.Sys_Soft_Reset), (16 bytes).
    Removing sys.o(i.Sys_Standby), (76 bytes).
    Removing sys.o(i.WFI_SET), (4 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(i.Usart1_SendChar), (24 bytes).
    Removing usart.o(i.Usart2_SendChar), (28 bytes).
    Removing usart.o(i.fputc), (28 bytes).
    Removing usart.o(.data), (4 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(i.KEY_Init), (44 bytes).
    Removing key.o(i.KEY_Scan), (88 bytes).
    Removing key.o(.data), (4 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing timer.o(i.TIM14_PWM_Init), (144 bytes).
    Removing timer.o(i.TIM5_CH1_Cap_Init), (200 bytes).
    Removing timer.o(i.TIM9_CH2_PWM_Init), (136 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(i.LED_Control), (76 bytes).
    Removing led.o(i.Led_Flash1), (40 bytes).
    Removing led.o(i.Led_Flash2), (72 bytes).
    Removing led.o(i.Led_Flash3), (44 bytes).
    Removing led.o(.data), (4 bytes).
    Removing can.o(.rev16_text), (4 bytes).
    Removing can.o(.revsh_text), (4 bytes).
    Removing can.o(i.CAN1_Msg_Pend), (40 bytes).
    Removing can.o(i.CAN1_Receive_Msg), (66 bytes).
    Removing can.o(i.CAN1_Send_Msg), (64 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(i.MYDMA_Config), (296 bytes).
    Removing dma.o(i.MYDMA_Enable), (32 bytes).
    Removing beep.o(.rev16_text), (4 bytes).
    Removing beep.o(.revsh_text), (4 bytes).
    Removing beep.o(i.BEEP_Control), (84 bytes).
    Removing beep.o(i.BEEP_Flash1), (68 bytes).
    Removing beep.o(i.BEEP_Flash2), (112 bytes).
    Removing beep.o(i.BEEP_Flash3), (68 bytes).
    Removing beep.o(i.BEEP_ShortRing), (24 bytes).
    Removing stmflash.o(.rev16_text), (4 bytes).
    Removing stmflash.o(.revsh_text), (4 bytes).
    Removing stmflash.o(i.STMFLASH_EraseSector), (116 bytes).
    Removing stmflash.o(i.STMFLASH_GetFlashSector), (160 bytes).
    Removing stmflash.o(i.STMFLASH_GetStatus), (64 bytes).
    Removing stmflash.o(i.STMFLASH_Lock), (20 bytes).
    Removing stmflash.o(i.STMFLASH_Read), (32 bytes).
    Removing stmflash.o(i.STMFLASH_ReadWord), (6 bytes).
    Removing stmflash.o(i.STMFLASH_Unlock), (24 bytes).
    Removing stmflash.o(i.STMFLASH_WaitDone), (40 bytes).
    Removing stmflash.o(i.STMFLASH_Write), (164 bytes).
    Removing stmflash.o(i.STMFLASH_WriteWord), (84 bytes).
    Removing bmi088.o(.rev16_text), (4 bytes).
    Removing bmi088.o(.revsh_text), (4 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(i.SPI1_SetSpeed), (44 bytes).

93 unused section(s) (total 4229 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/asinf.c                       0x00000000   Number         0  asinf_x.o ABSOLUTE
    ../mathlib/asinf.c                       0x00000000   Number         0  asinf.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f_x.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ..\HARDWARE\BEEP\beep.c                  0x00000000   Number         0  beep.o ABSOLUTE
    ..\HARDWARE\BMI\BMI088.c                 0x00000000   Number         0  bmi088.o ABSOLUTE
    ..\HARDWARE\CAN\can.c                    0x00000000   Number         0  can.o ABSOLUTE
    ..\HARDWARE\DMA\dma.c                    0x00000000   Number         0  dma.o ABSOLUTE
    ..\HARDWARE\KEY\key.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\MahonyAHRS\MahonyAHRS.c      0x00000000   Number         0  mahonyahrs.o ABSOLUTE
    ..\HARDWARE\SPI\spi.c                    0x00000000   Number         0  spi.o ABSOLUTE
    ..\HARDWARE\STMFLASH\stmflash.c          0x00000000   Number         0  stmflash.o ABSOLUTE
    ..\HARDWARE\TIMER\timer.c                0x00000000   Number         0  timer.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\startup_stm32f407xx.s          0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\HARDWARE\\BEEP\\beep.c               0x00000000   Number         0  beep.o ABSOLUTE
    ..\\HARDWARE\\BMI\\BMI088.c              0x00000000   Number         0  bmi088.o ABSOLUTE
    ..\\HARDWARE\\CAN\\can.c                 0x00000000   Number         0  can.o ABSOLUTE
    ..\\HARDWARE\\DMA\\dma.c                 0x00000000   Number         0  dma.o ABSOLUTE
    ..\\HARDWARE\\KEY\\key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HARDWARE\\LED\\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\HARDWARE\\SPI\\spi.c                 0x00000000   Number         0  spi.o ABSOLUTE
    ..\\HARDWARE\\STMFLASH\\stmflash.c       0x00000000   Number         0  stmflash.o ABSOLUTE
    ..\\HARDWARE\\TIMER\\timer.c             0x00000000   Number         0  timer.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    Control.c                                0x00000000   Number         0  control.o ABSOLUTE
    Control.c                                0x00000000   Number         0  control.o ABSOLUTE
    DJI_CAN_Motor.c                          0x00000000   Number         0  dji_can_motor.o ABSOLUTE
    DJI_CAN_Motor.c                          0x00000000   Number         0  dji_can_motor.o ABSOLUTE
    Data_Transfer.c                          0x00000000   Number         0  data_transfer.o ABSOLUTE
    Data_Transfer.c                          0x00000000   Number         0  data_transfer.o ABSOLUTE
    Dbus_DR16.c                              0x00000000   Number         0  dbus_dr16.o ABSOLUTE
    Dbus_DR16.c                              0x00000000   Number         0  dbus_dr16.o ABSOLUTE
    Motor_move.c                             0x00000000   Number         0  motor_move.o ABSOLUTE
    Motor_move.c                             0x00000000   Number         0  motor_move.o ABSOLUTE
    PWM_Motor.c                              0x00000000   Number         0  pwm_motor.o ABSOLUTE
    PWM_Motor.c                              0x00000000   Number         0  pwm_motor.o ABSOLUTE
    bluetooth.c                              0x00000000   Number         0  bluetooth.o ABSOLUTE
    bluetooth.c                              0x00000000   Number         0  bluetooth.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    test.c                                   0x00000000   Number         0  test.o ABSOLUTE
    test.c                                   0x00000000   Number         0  test.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001c4   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000220   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x0800023c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800023e   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000242   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000242   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000244   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000246   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000246   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000246   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000246   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000246   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000246   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000246   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000248   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000248   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000248   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800024e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800024e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000252   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000252   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800025a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800025c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800025c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000260   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000268   Section       72  startup_stm32f407xx.o(.text)
    $v0                                      0x08000268   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x080002b0   Section        2  use_no_semi_2.o(.text)
    .text                                    0x080002b2   Section        0  heapauxi.o(.text)
    .text                                    0x080002b8   Section        2  use_no_semi.o(.text)
    .text                                    0x080002ba   Section        0  _rserrno.o(.text)
    .text                                    0x080002d0   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x080002d8   Section        8  libspace.o(.text)
    .text                                    0x080002e0   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800032a   Section        0  exit.o(.text)
    i.BEEP_Init                              0x0800033c   Section        0  beep.o(i.BEEP_Init)
    i.BMI088_Callback                        0x080003e4   Section        0  bmi088.o(i.BMI088_Callback)
    i.BMI088_initial                         0x080005a8   Section        0  bmi088.o(i.BMI088_initial)
    i.CAN1_Mode_Init                         0x0800066c   Section        0  can.o(i.CAN1_Mode_Init)
    i.CAN1_Motor_Init                        0x08000844   Section        0  dji_can_motor.o(i.CAN1_Motor_Init)
    i.CAN1_RX0_IRQHandler                    0x08000858   Section        0  dji_can_motor.o(i.CAN1_RX0_IRQHandler)
    i.CAN1_Rx_Msg                            0x08000874   Section        0  can.o(i.CAN1_Rx_Msg)
    i.CAN1_Tx_Msg                            0x0800094c   Section        0  can.o(i.CAN1_Tx_Msg)
    i.CAN1_Tx_Staus                          0x08000a58   Section        0  can.o(i.CAN1_Tx_Staus)
    i.Can_Sevo_Control                       0x08000adc   Section        0  dji_can_motor.o(i.Can_Sevo_Control)
    i.DMAUART_Enable                         0x08000b4c   Section        0  data_transfer.o(i.DMAUART_Enable)
    i.DMAUART_Send_Config                    0x08000b6c   Section        0  data_transfer.o(i.DMAUART_Send_Config)
    i.Dbus_DMA_Config                        0x08000c94   Section        0  dbus_dr16.o(i.Dbus_DMA_Config)
    i.Dbus_DR16_Init                         0x08000e38   Section        0  dbus_dr16.o(i.Dbus_DR16_Init)
    i.GPIO_AF_Set                            0x08000e6c   Section        0  sys.o(i.GPIO_AF_Set)
    i.GPIO_Set                               0x08000eac   Section        0  sys.o(i.GPIO_Set)
    i.LED_Init                               0x08000f8c   Section        0  led.o(i.LED_Init)
    i.MY_NVIC_Init                           0x08000fd4   Section        0  sys.o(i.MY_NVIC_Init)
    i.MY_NVIC_PriorityGroupConfig            0x0800104c   Section        0  sys.o(i.MY_NVIC_PriorityGroupConfig)
    i.MY_NVIC_SetVectorTable                 0x08001074   Section        0  sys.o(i.MY_NVIC_SetVectorTable)
    i.MahonyAHRSupdateIMU                    0x08001084   Section        0  mahonyahrs.o(i.MahonyAHRSupdateIMU)
    i.Motor_Back                             0x08001380   Section        0  motor_move.o(i.Motor_Back)
    i.Motor_Back1                            0x08001404   Section        0  motor_move.o(i.Motor_Back1)
    i.Motor_Back2                            0x08001480   Section        0  motor_move.o(i.Motor_Back2)
    i.Motor_Left                             0x08001504   Section        0  motor_move.o(i.Motor_Left)
    i.Motor_Left1                            0x08001654   Section        0  motor_move.o(i.Motor_Left1)
    i.Motor_Left2                            0x080017a2   Section        0  motor_move.o(i.Motor_Left2)
    i.Motor_Right                            0x08001a72   Section        0  motor_move.o(i.Motor_Right)
    i.Motor_Right1                           0x08001bc2   Section        0  motor_move.o(i.Motor_Right1)
    i.Motor_Right2                           0x08001d12   Section        0  motor_move.o(i.Motor_Right2)
    i.Motor_Walk                             0x08001fe0   Section        0  motor_move.o(i.Motor_Walk)
    i.Motor_Walk1                            0x08002064   Section        0  motor_move.o(i.Motor_Walk1)
    i.Motor_Walk3                            0x080020e0   Section        0  motor_move.o(i.Motor_Walk3)
    i.Motor_Walk4                            0x0800216c   Section        0  motor_move.o(i.Motor_Walk4)
    i.Motor_ctrl                             0x080021f0   Section        0  motor_move.o(i.Motor_ctrl)
    i.Motor_move                             0x08002218   Section        0  motor_move.o(i.Motor_move)
    i.Motor_stand                            0x080024f4   Section        0  motor_move.o(i.Motor_stand)
    i.Motor_stand1                           0x0800251c   Section        0  motor_move.o(i.Motor_stand1)
    i.Motor_walk2                            0x08002544   Section        0  motor_move.o(i.Motor_walk2)
    i.PWM1_Motor_Init                        0x080028a8   Section        0  pwm_motor.o(i.PWM1_Motor_Init)
    i.PWM2_Motor_Init                        0x08002a18   Section        0  pwm_motor.o(i.PWM2_Motor_Init)
    i.RemoteDataProcess                      0x08002b60   Section        0  dbus_dr16.o(i.RemoteDataProcess)
    i.SPI1_Init                              0x08002c1c   Section        0  spi.o(i.SPI1_Init)
    i.SPI1_ReadWriteByte                     0x08002cf0   Section        0  spi.o(i.SPI1_ReadWriteByte)
    i.Stm32_Clock_Init                       0x08002d20   Section        0  sys.o(i.Stm32_Clock_Init)
    i.Sys_Clock_Set                          0x08002d88   Section        0  sys.o(i.Sys_Clock_Set)
    i.TIM3_IRQHandler                        0x08002e94   Section        0  control.o(i.TIM3_IRQHandler)
    i.TIM3_Int_Init                          0x08002ed8   Section        0  timer.o(i.TIM3_Int_Init)
    i.TIM5_IRQHandler                        0x08002f28   Section        0  timer.o(i.TIM5_IRQHandler)
    i.UART4_IRQHandler                       0x0800300c   Section        0  dbus_dr16.o(i.UART4_IRQHandler)
    i.__ARM_fpclassifyf                      0x080030bc   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__hardfp_asinf                         0x080030e4   Section        0  asinf.o(i.__hardfp_asinf)
    i.__hardfp_atan2f                        0x08003210   Section        0  atan2f.o(i.__hardfp_atan2f)
    i.__mathlib_flt_infnan                   0x08003460   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_infnan2                  0x08003466   Section        0  funder.o(i.__mathlib_flt_infnan2)
    i.__mathlib_flt_invalid                  0x0800346c   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x0800347c   Section        0  funder.o(i.__mathlib_flt_underflow)
    i._sys_exit                              0x0800348c   Section        0  usart.o(i._sys_exit)
    i.delay_init                             0x08003490   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x080034d0   Section        0  delay.o(i.delay_ms)
    i.delay_xms                              0x08003508   Section        0  delay.o(i.delay_xms)
    i.get_angle                              0x08003544   Section        0  mahonyahrs.o(i.get_angle)
    i.invSqrt                                0x0800363c   Section        0  mahonyahrs.o(i.invSqrt)
    i.main                                   0x08003678   Section        0  test.o(i.main)
    i.sqrtf                                  0x08003fc8   Section        0  sqrtf.o(i.sqrtf)
    i.uart4_init                             0x08004008   Section        0  dbus_dr16.o(i.uart4_init)
    i.uart_init                              0x0800413c   Section        0  usart.o(i.uart_init)
    x$fpl$fpinit                             0x0800423c   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800423c   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$usenofp                            0x08004246   Section        0  usenofp.o(x$fpl$usenofp)
    .data                                    0x20000000   Section      344  motor_move.o(.data)
    .data                                    0x20000158   Section        8  test.o(.data)
    .data                                    0x20000160   Section       92  dji_can_motor.o(.data)
    .data                                    0x200001bc   Section        5  control.o(.data)
    .data                                    0x200001c2   Section        2  dbus_dr16.o(.data)
    .data                                    0x200001c4   Section        4  delay.o(.data)
    fac_us                                   0x200001c4   Data           1  delay.o(.data)
    fac_ms                                   0x200001c6   Data           2  delay.o(.data)
    .data                                    0x200001c8   Section        8  timer.o(.data)
    .data                                    0x200001d0   Section        4  beep.o(.data)
    .data                                    0x200001d4   Section       36  bmi088.o(.data)
    .data                                    0x200001f8   Section       20  mahonyahrs.o(.data)
    .bss                                     0x2000020c   Section       58  dbus_dr16.o(.bss)
    .bss                                     0x20000246   Section       32  data_transfer.o(.bss)
    .bss                                     0x20000268   Section       24  bmi088.o(.bss)
    .bss                                     0x20000280   Section       96  libspace.o(.bss)
    HEAP                                     0x200002e0   Section     8192  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x200002e0   Data        8192  startup_stm32f407xx.o(HEAP)
    STACK                                    0x200022e0   Section     8192  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x200022e0   Data        8192  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x200042e0   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  test.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001c5   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001c5   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000221   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x0800023d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800023f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000243   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000245   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000247   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000247   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000247   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000247   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000247   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000247   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000247   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000249   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000249   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000249   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800024f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800024f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000253   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000253   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800025b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800025d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800025d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000261   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000269   Thumb Code    14  startup_stm32f407xx.o(.text)
    NMI_Handler                              0x08000277   Thumb Code     2  startup_stm32f407xx.o(.text)
    HardFault_Handler                        0x08000279   Thumb Code     2  startup_stm32f407xx.o(.text)
    MemManage_Handler                        0x0800027b   Thumb Code     2  startup_stm32f407xx.o(.text)
    BusFault_Handler                         0x0800027d   Thumb Code     2  startup_stm32f407xx.o(.text)
    UsageFault_Handler                       0x0800027f   Thumb Code     2  startup_stm32f407xx.o(.text)
    SVC_Handler                              0x08000281   Thumb Code     2  startup_stm32f407xx.o(.text)
    DebugMon_Handler                         0x08000283   Thumb Code     2  startup_stm32f407xx.o(.text)
    PendSV_Handler                           0x08000285   Thumb Code     2  startup_stm32f407xx.o(.text)
    SysTick_Handler                          0x08000287   Thumb Code     2  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART1_IRQHandler                        0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x08000289   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x0800028d   Thumb Code     0  startup_stm32f407xx.o(.text)
    __use_no_semihosting                     0x080002b1   Thumb Code     2  use_no_semi_2.o(.text)
    __use_two_region_memory                  0x080002b3   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080002b5   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080002b7   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x080002b9   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080002b9   Thumb Code     2  use_no_semi.o(.text)
    __read_errno                             0x080002bb   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080002c5   Thumb Code    12  _rserrno.o(.text)
    __aeabi_errno_addr                       0x080002d1   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x080002d1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x080002d1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __user_libspace                          0x080002d9   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080002d9   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080002d9   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080002e1   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0800032b   Thumb Code    18  exit.o(.text)
    BEEP_Init                                0x0800033d   Thumb Code   156  beep.o(i.BEEP_Init)
    BMI088_Callback                          0x080003e5   Thumb Code   406  bmi088.o(i.BMI088_Callback)
    BMI088_initial                           0x080005a9   Thumb Code   172  bmi088.o(i.BMI088_initial)
    CAN1_Mode_Init                           0x0800066d   Thumb Code   456  can.o(i.CAN1_Mode_Init)
    CAN1_Motor_Init                          0x08000845   Thumb Code    20  dji_can_motor.o(i.CAN1_Motor_Init)
    CAN1_RX0_IRQHandler                      0x08000859   Thumb Code    28  dji_can_motor.o(i.CAN1_RX0_IRQHandler)
    CAN1_Rx_Msg                              0x08000875   Thumb Code   208  can.o(i.CAN1_Rx_Msg)
    CAN1_Tx_Msg                              0x0800094d   Thumb Code   260  can.o(i.CAN1_Tx_Msg)
    CAN1_Tx_Staus                            0x08000a59   Thumb Code   126  can.o(i.CAN1_Tx_Staus)
    Can_Sevo_Control                         0x08000add   Thumb Code    98  dji_can_motor.o(i.Can_Sevo_Control)
    DMAUART_Enable                           0x08000b4d   Thumb Code    32  data_transfer.o(i.DMAUART_Enable)
    DMAUART_Send_Config                      0x08000b6d   Thumb Code   284  data_transfer.o(i.DMAUART_Send_Config)
    Dbus_DMA_Config                          0x08000c95   Thumb Code   408  dbus_dr16.o(i.Dbus_DMA_Config)
    Dbus_DR16_Init                           0x08000e39   Thumb Code    34  dbus_dr16.o(i.Dbus_DR16_Init)
    GPIO_AF_Set                              0x08000e6d   Thumb Code    64  sys.o(i.GPIO_AF_Set)
    GPIO_Set                                 0x08000ead   Thumb Code   222  sys.o(i.GPIO_Set)
    LED_Init                                 0x08000f8d   Thumb Code    56  led.o(i.LED_Init)
    MY_NVIC_Init                             0x08000fd5   Thumb Code   116  sys.o(i.MY_NVIC_Init)
    MY_NVIC_PriorityGroupConfig              0x0800104d   Thumb Code    32  sys.o(i.MY_NVIC_PriorityGroupConfig)
    MY_NVIC_SetVectorTable                   0x08001075   Thumb Code    12  sys.o(i.MY_NVIC_SetVectorTable)
    MahonyAHRSupdateIMU                      0x08001085   Thumb Code   730  mahonyahrs.o(i.MahonyAHRSupdateIMU)
    Motor_Back                               0x08001381   Thumb Code   120  motor_move.o(i.Motor_Back)
    Motor_Back1                              0x08001405   Thumb Code   116  motor_move.o(i.Motor_Back1)
    Motor_Back2                              0x08001481   Thumb Code   118  motor_move.o(i.Motor_Back2)
    Motor_Left                               0x08001505   Thumb Code   336  motor_move.o(i.Motor_Left)
    Motor_Left1                              0x08001655   Thumb Code   334  motor_move.o(i.Motor_Left1)
    Motor_Left2                              0x080017a3   Thumb Code   720  motor_move.o(i.Motor_Left2)
    Motor_Right                              0x08001a73   Thumb Code   336  motor_move.o(i.Motor_Right)
    Motor_Right1                             0x08001bc3   Thumb Code   336  motor_move.o(i.Motor_Right1)
    Motor_Right2                             0x08001d13   Thumb Code   718  motor_move.o(i.Motor_Right2)
    Motor_Walk                               0x08001fe1   Thumb Code   120  motor_move.o(i.Motor_Walk)
    Motor_Walk1                              0x08002065   Thumb Code   116  motor_move.o(i.Motor_Walk1)
    Motor_Walk3                              0x080020e1   Thumb Code   124  motor_move.o(i.Motor_Walk3)
    Motor_Walk4                              0x0800216d   Thumb Code   120  motor_move.o(i.Motor_Walk4)
    Motor_ctrl                               0x080021f1   Thumb Code    34  motor_move.o(i.Motor_ctrl)
    Motor_move                               0x08002219   Thumb Code   728  motor_move.o(i.Motor_move)
    Motor_stand                              0x080024f5   Thumb Code    34  motor_move.o(i.Motor_stand)
    Motor_stand1                             0x0800251d   Thumb Code    34  motor_move.o(i.Motor_stand1)
    Motor_walk2                              0x08002545   Thumb Code   864  motor_move.o(i.Motor_walk2)
    PWM1_Motor_Init                          0x080028a9   Thumb Code   350  pwm_motor.o(i.PWM1_Motor_Init)
    PWM2_Motor_Init                          0x08002a19   Thumb Code   316  pwm_motor.o(i.PWM2_Motor_Init)
    RemoteDataProcess                        0x08002b61   Thumb Code   182  dbus_dr16.o(i.RemoteDataProcess)
    SPI1_Init                                0x08002c1d   Thumb Code   194  spi.o(i.SPI1_Init)
    SPI1_ReadWriteByte                       0x08002cf1   Thumb Code    42  spi.o(i.SPI1_ReadWriteByte)
    Stm32_Clock_Init                         0x08002d21   Thumb Code    90  sys.o(i.Stm32_Clock_Init)
    Sys_Clock_Set                            0x08002d89   Thumb Code   254  sys.o(i.Sys_Clock_Set)
    TIM3_IRQHandler                          0x08002e95   Thumb Code    54  control.o(i.TIM3_IRQHandler)
    TIM3_Int_Init                            0x08002ed9   Thumb Code    72  timer.o(i.TIM3_Int_Init)
    TIM5_IRQHandler                          0x08002f29   Thumb Code   214  timer.o(i.TIM5_IRQHandler)
    UART4_IRQHandler                         0x0800300d   Thumb Code   160  dbus_dr16.o(i.UART4_IRQHandler)
    __ARM_fpclassifyf                        0x080030bd   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_asinf                           0x080030e5   Thumb Code   258  asinf.o(i.__hardfp_asinf)
    __hardfp_atan2f                          0x08003211   Thumb Code   502  atan2f.o(i.__hardfp_atan2f)
    __mathlib_flt_infnan                     0x08003461   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_infnan2                    0x08003467   Thumb Code     6  funder.o(i.__mathlib_flt_infnan2)
    __mathlib_flt_invalid                    0x0800346d   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x0800347d   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    _sys_exit                                0x0800348d   Thumb Code     4  usart.o(i._sys_exit)
    delay_init                               0x08003491   Thumb Code    56  delay.o(i.delay_init)
    delay_ms                                 0x080034d1   Thumb Code    56  delay.o(i.delay_ms)
    delay_xms                                0x08003509   Thumb Code    56  delay.o(i.delay_xms)
    get_angle                                0x08003545   Thumb Code   244  mahonyahrs.o(i.get_angle)
    invSqrt                                  0x0800363d   Thumb Code    56  mahonyahrs.o(i.invSqrt)
    main                                     0x08003679   Thumb Code  2378  test.o(i.main)
    sqrtf                                    0x08003fc9   Thumb Code    62  sqrtf.o(i.sqrtf)
    uart4_init                               0x08004009   Thumb Code   292  dbus_dr16.o(i.uart4_init)
    uart_init                                0x0800413d   Thumb Code   234  usart.o(i.uart_init)
    _fp_init                                 0x0800423d   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08004245   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08004245   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __I$use$fp                               0x08004246   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08004248   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08004268   Number         0  anon$$obj.o(Region$$Table)
    i                                        0x20000000   Data           4  motor_move.o(.data)
    temp                                     0x20000004   Data           4  motor_move.o(.data)
    w                                        0x20000008   Data          48  motor_move.o(.data)
    s                                        0x20000038   Data          48  motor_move.o(.data)
    r                                        0x20000068   Data          48  motor_move.o(.data)
    z                                        0x20000098   Data          48  motor_move.o(.data)
    n                                        0x200000c8   Data          48  motor_move.o(.data)
    m                                        0x200000f8   Data          48  motor_move.o(.data)
    k                                        0x20000128   Data          48  motor_move.o(.data)
    Delay                                    0x20000158   Data           4  test.o(.data)
    change                                   0x2000015c   Data           4  test.o(.data)
    can_key                                  0x20000160   Data           1  dji_can_motor.o(.data)
    CAN_SendBuf                              0x20000161   Data           8  dji_can_motor.o(.data)
    CAN_RecvBuf                              0x20000169   Data           8  dji_can_motor.o(.data)
    CAN_timeout                              0x20000172   Data           2  dji_can_motor.o(.data)
    mbox                                     0x20000174   Data           1  dji_can_motor.o(.data)
    CAN_CarMotor_OffLine                     0x20000175   Data           4  dji_can_motor.o(.data)
    CAN_CloudMotor_OffLine                   0x20000179   Data           2  dji_can_motor.o(.data)
    CAN_ShootMotor_OffLine                   0x2000017b   Data           2  dji_can_motor.o(.data)
    CarMotorNowCurrent                       0x2000017e   Data           8  dji_can_motor.o(.data)
    CarMotorSetSpeed                         0x20000186   Data           8  dji_can_motor.o(.data)
    CarMotorNowSpeed                         0x2000018e   Data           8  dji_can_motor.o(.data)
    ShootMotorNowCurrent                     0x20000196   Data           6  dji_can_motor.o(.data)
    ShootMotorSetSpeed                       0x2000019c   Data           6  dji_can_motor.o(.data)
    ShootMotorNowSpeed                       0x200001a2   Data           6  dji_can_motor.o(.data)
    CloudMotorNowCurrent                     0x200001a8   Data           4  dji_can_motor.o(.data)
    CloudMotorSetSpeed                       0x200001ac   Data           4  dji_can_motor.o(.data)
    CloudMotorNowSpeed                       0x200001b0   Data           4  dji_can_motor.o(.data)
    CloudMotorSetAngle                       0x200001b4   Data           4  dji_can_motor.o(.data)
    CloudMotorNowAngle                       0x200001b8   Data           4  dji_can_motor.o(.data)
    ARMED                                    0x200001bc   Data           1  control.o(.data)
    SendStick                                0x200001bd   Data           1  control.o(.data)
    Tim3_stick                               0x200001be   Data           1  control.o(.data)
    Cloud_ctrl_stick                         0x200001bf   Data           1  control.o(.data)
    Tim3_Flag                                0x200001c0   Data           1  control.o(.data)
    RC_OffLine                               0x200001c2   Data           2  dbus_dr16.o(.data)
    TIM5CH1_CAPTURE_STA                      0x200001c8   Data           1  timer.o(.data)
    TIM5CH1_CAPTURE_VAL                      0x200001cc   Data           4  timer.o(.data)
    BEEP_state                               0x200001d0   Data           1  beep.o(.data)
    BEEP_timetick                            0x200001d2   Data           2  beep.o(.data)
    BMI088_ACCEL_SEN                         0x200001d4   Data           4  bmi088.o(.data)
    BMI088_GYRO_SEN                          0x200001d8   Data           4  bmi088.o(.data)
    quat                                     0x200001dc   Data          16  bmi088.o(.data)
    Pitch                                    0x200001ec   Data           4  bmi088.o(.data)
    Roll                                     0x200001f0   Data           4  bmi088.o(.data)
    Yaw                                      0x200001f4   Data           4  bmi088.o(.data)
    twoKp                                    0x200001f8   Data           4  mahonyahrs.o(.data)
    twoKi                                    0x200001fc   Data           4  mahonyahrs.o(.data)
    integralFBx                              0x20000200   Data           4  mahonyahrs.o(.data)
    integralFBy                              0x20000204   Data           4  mahonyahrs.o(.data)
    integralFBz                              0x20000208   Data           4  mahonyahrs.o(.data)
    Dbus_buf                                 0x2000020c   Data          36  dbus_dr16.o(.bss)
    RC_CtrlData                              0x20000230   Data          22  dbus_dr16.o(.bss)
    data_to_send                             0x20000246   Data          32  data_transfer.o(.bss)
    BMI088_accelerometer                     0x20000268   Data          12  bmi088.o(.bss)
    BMI088_gyro                              0x20000274   Data          12  bmi088.o(.bss)
    __libspace_start                         0x20000280   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200002e0   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00004474, Max: 0x00100000, ABSOLUTE, COMPRESSED[0x00004374])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00004268, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         1130  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         1347    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000005a   Code   RO         1345    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x0800021e   0x0800021e   0x00000002   PAD
    0x08000220   0x08000220   0x0000001c   Code   RO         1349    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800023c   0x0800023c   0x00000002   Code   RO         1222    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800023e   0x0800023e   0x00000004   Code   RO         1225    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1228    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1231    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1233    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1235    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1238    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1240    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1242    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1244    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1246    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1248    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1250    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1252    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1254    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1256    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1258    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1262    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1264    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1266    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000000   Code   RO         1268    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000242   0x08000242   0x00000002   Code   RO         1269    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000244   0x08000244   0x00000002   Code   RO         1287    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000246   0x08000246   0x00000000   Code   RO         1297    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000246   0x08000246   0x00000000   Code   RO         1299    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000246   0x08000246   0x00000000   Code   RO         1302    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000246   0x08000246   0x00000000   Code   RO         1305    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000246   0x08000246   0x00000000   Code   RO         1307    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000246   0x08000246   0x00000000   Code   RO         1310    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000246   0x08000246   0x00000002   Code   RO         1311    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         1158    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000248   0x08000248   0x00000000   Code   RO         1191    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000248   0x08000248   0x00000006   Code   RO         1203    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800024e   0x0800024e   0x00000000   Code   RO         1193    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800024e   0x0800024e   0x00000004   Code   RO         1194    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         1196    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000252   0x08000252   0x00000008   Code   RO         1197    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800025a   0x0800025a   0x00000002   Code   RO         1223    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800025c   0x0800025c   0x00000000   Code   RO         1271    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800025c   0x0800025c   0x00000004   Code   RO         1272    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000260   0x08000260   0x00000006   Code   RO         1273    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000266   0x08000266   0x00000002   PAD
    0x08000268   0x08000268   0x00000048   Code   RO            4    .text               startup_stm32f407xx.o
    0x080002b0   0x080002b0   0x00000002   Code   RO         1126    .text               c_w.l(use_no_semi_2.o)
    0x080002b2   0x080002b2   0x00000006   Code   RO         1128    .text               c_w.l(heapauxi.o)
    0x080002b8   0x080002b8   0x00000002   Code   RO         1156    .text               c_w.l(use_no_semi.o)
    0x080002ba   0x080002ba   0x00000016   Code   RO         1159    .text               c_w.l(_rserrno.o)
    0x080002d0   0x080002d0   0x00000008   Code   RO         1208    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x080002d8   0x080002d8   0x00000008   Code   RO         1210    .text               c_w.l(libspace.o)
    0x080002e0   0x080002e0   0x0000004a   Code   RO         1213    .text               c_w.l(sys_stackheap_outer.o)
    0x0800032a   0x0800032a   0x00000012   Code   RO         1215    .text               c_w.l(exit.o)
    0x0800033c   0x0800033c   0x000000a8   Code   RO          901    i.BEEP_Init         beep.o
    0x080003e4   0x080003e4   0x000001c4   Code   RO         1027    i.BMI088_Callback   bmi088.o
    0x080005a8   0x080005a8   0x000000c4   Code   RO         1028    i.BMI088_initial    bmi088.o
    0x0800066c   0x0800066c   0x000001d8   Code   RO          815    i.CAN1_Mode_Init    can.o
    0x08000844   0x08000844   0x00000014   Code   RO          275    i.CAN1_Motor_Init   dji_can_motor.o
    0x08000858   0x08000858   0x0000001c   Code   RO          276    i.CAN1_RX0_IRQHandler  dji_can_motor.o
    0x08000874   0x08000874   0x000000d8   Code   RO          818    i.CAN1_Rx_Msg       can.o
    0x0800094c   0x0800094c   0x0000010c   Code   RO          820    i.CAN1_Tx_Msg       can.o
    0x08000a58   0x08000a58   0x00000084   Code   RO          821    i.CAN1_Tx_Staus     can.o
    0x08000adc   0x08000adc   0x00000070   Code   RO          277    i.Can_Sevo_Control  dji_can_motor.o
    0x08000b4c   0x08000b4c   0x00000020   Code   RO          459    i.DMAUART_Enable    data_transfer.o
    0x08000b6c   0x08000b6c   0x00000128   Code   RO          460    i.DMAUART_Send_Config  data_transfer.o
    0x08000c94   0x08000c94   0x000001a4   Code   RO          405    i.Dbus_DMA_Config   dbus_dr16.o
    0x08000e38   0x08000e38   0x00000034   Code   RO          406    i.Dbus_DR16_Init    dbus_dr16.o
    0x08000e6c   0x08000e6c   0x00000040   Code   RO          544    i.GPIO_AF_Set       sys.o
    0x08000eac   0x08000eac   0x000000de   Code   RO          545    i.GPIO_Set          sys.o
    0x08000f8a   0x08000f8a   0x00000002   PAD
    0x08000f8c   0x08000f8c   0x00000048   Code   RO          769    i.LED_Init          led.o
    0x08000fd4   0x08000fd4   0x00000078   Code   RO          548    i.MY_NVIC_Init      sys.o
    0x0800104c   0x0800104c   0x00000028   Code   RO          549    i.MY_NVIC_PriorityGroupConfig  sys.o
    0x08001074   0x08001074   0x00000010   Code   RO          550    i.MY_NVIC_SetVectorTable  sys.o
    0x08001084   0x08001084   0x000002fc   Code   RO         1097    i.MahonyAHRSupdateIMU  mahonyahrs.o
    0x08001380   0x08001380   0x00000084   Code   RO           12    i.Motor_Back        motor_move.o
    0x08001404   0x08001404   0x0000007c   Code   RO           13    i.Motor_Back1       motor_move.o
    0x08001480   0x08001480   0x00000084   Code   RO           14    i.Motor_Back2       motor_move.o
    0x08001504   0x08001504   0x00000150   Code   RO           16    i.Motor_Left        motor_move.o
    0x08001654   0x08001654   0x0000014e   Code   RO           17    i.Motor_Left1       motor_move.o
    0x080017a2   0x080017a2   0x000002d0   Code   RO           18    i.Motor_Left2       motor_move.o
    0x08001a72   0x08001a72   0x00000150   Code   RO           19    i.Motor_Right       motor_move.o
    0x08001bc2   0x08001bc2   0x00000150   Code   RO           20    i.Motor_Right1      motor_move.o
    0x08001d12   0x08001d12   0x000002ce   Code   RO           21    i.Motor_Right2      motor_move.o
    0x08001fe0   0x08001fe0   0x00000084   Code   RO           22    i.Motor_Walk        motor_move.o
    0x08002064   0x08002064   0x0000007c   Code   RO           23    i.Motor_Walk1       motor_move.o
    0x080020e0   0x080020e0   0x0000008c   Code   RO           25    i.Motor_Walk3       motor_move.o
    0x0800216c   0x0800216c   0x00000084   Code   RO           26    i.Motor_Walk4       motor_move.o
    0x080021f0   0x080021f0   0x00000028   Code   RO           29    i.Motor_ctrl        motor_move.o
    0x08002218   0x08002218   0x000002dc   Code   RO           30    i.Motor_move        motor_move.o
    0x080024f4   0x080024f4   0x00000028   Code   RO           31    i.Motor_stand       motor_move.o
    0x0800251c   0x0800251c   0x00000028   Code   RO           32    i.Motor_stand1      motor_move.o
    0x08002544   0x08002544   0x00000364   Code   RO           33    i.Motor_walk2       motor_move.o
    0x080028a8   0x080028a8   0x00000170   Code   RO          379    i.PWM1_Motor_Init   pwm_motor.o
    0x08002a18   0x08002a18   0x00000148   Code   RO          380    i.PWM2_Motor_Init   pwm_motor.o
    0x08002b60   0x08002b60   0x000000bc   Code   RO          407    i.RemoteDataProcess  dbus_dr16.o
    0x08002c1c   0x08002c1c   0x000000d4   Code   RO         1067    i.SPI1_Init         spi.o
    0x08002cf0   0x08002cf0   0x00000030   Code   RO         1068    i.SPI1_ReadWriteByte  spi.o
    0x08002d20   0x08002d20   0x00000068   Code   RO          551    i.Stm32_Clock_Init  sys.o
    0x08002d88   0x08002d88   0x0000010c   Code   RO          552    i.Sys_Clock_Set     sys.o
    0x08002e94   0x08002e94   0x00000044   Code   RO          310    i.TIM3_IRQHandler   control.o
    0x08002ed8   0x08002ed8   0x00000050   Code   RO          722    i.TIM3_Int_Init     timer.o
    0x08002f28   0x08002f28   0x000000e4   Code   RO          724    i.TIM5_IRQHandler   timer.o
    0x0800300c   0x0800300c   0x000000b0   Code   RO          408    i.UART4_IRQHandler  dbus_dr16.o
    0x080030bc   0x080030bc   0x00000026   Code   RO         1162    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x080030e2   0x080030e2   0x00000002   PAD
    0x080030e4   0x080030e4   0x0000012c   Code   RO         1132    i.__hardfp_asinf    m_wm.l(asinf.o)
    0x08003210   0x08003210   0x00000250   Code   RO         1144    i.__hardfp_atan2f   m_wm.l(atan2f.o)
    0x08003460   0x08003460   0x00000006   Code   RO         1165    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x08003466   0x08003466   0x00000006   Code   RO         1166    i.__mathlib_flt_infnan2  m_wm.l(funder.o)
    0x0800346c   0x0800346c   0x00000010   Code   RO         1167    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x0800347c   0x0800347c   0x00000010   Code   RO         1170    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x0800348c   0x0800348c   0x00000004   Code   RO          640    i._sys_exit         usart.o
    0x08003490   0x08003490   0x00000040   Code   RO          502    i.delay_init        delay.o
    0x080034d0   0x080034d0   0x00000038   Code   RO          503    i.delay_ms          delay.o
    0x08003508   0x08003508   0x0000003c   Code   RO          505    i.delay_xms         delay.o
    0x08003544   0x08003544   0x000000f8   Code   RO         1098    i.get_angle         mahonyahrs.o
    0x0800363c   0x0800363c   0x0000003c   Code   RO         1099    i.invSqrt           mahonyahrs.o
    0x08003678   0x08003678   0x00000950   Code   RO          218    i.main              test.o
    0x08003fc8   0x08003fc8   0x0000003e   Code   RO         1180    i.sqrtf             m_wm.l(sqrtf.o)
    0x08004006   0x08004006   0x00000002   PAD
    0x08004008   0x08004008   0x00000134   Code   RO          409    i.uart4_init        dbus_dr16.o
    0x0800413c   0x0800413c   0x00000100   Code   RO          642    i.uart_init         usart.o
    0x0800423c   0x0800423c   0x0000000a   Code   RO         1279    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08004246   0x08004246   0x00000000   Code   RO         1161    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08004246   0x08004246   0x00000002   PAD
    0x08004248   0x08004248   0x00000020   Data   RO         1343    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08004268, Size: 0x000042e0, Max: 0x00020000, ABSOLUTE, COMPRESSED[0x0000010c])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000158   Data   RW           34    .data               motor_move.o
    0x20000158   COMPRESSED   0x00000008   Data   RW          219    .data               test.o
    0x20000160   COMPRESSED   0x0000005c   Data   RW          278    .data               dji_can_motor.o
    0x200001bc   COMPRESSED   0x00000005   Data   RW          311    .data               control.o
    0x200001c1   COMPRESSED   0x00000001   PAD
    0x200001c2   COMPRESSED   0x00000002   Data   RW          411    .data               dbus_dr16.o
    0x200001c4   COMPRESSED   0x00000004   Data   RW          506    .data               delay.o
    0x200001c8   COMPRESSED   0x00000008   Data   RW          726    .data               timer.o
    0x200001d0   COMPRESSED   0x00000004   Data   RW          903    .data               beep.o
    0x200001d4   COMPRESSED   0x00000024   Data   RW         1030    .data               bmi088.o
    0x200001f8   COMPRESSED   0x00000014   Data   RW         1100    .data               mahonyahrs.o
    0x2000020c        -       0x0000003a   Zero   RW          410    .bss                dbus_dr16.o
    0x20000246        -       0x00000020   Zero   RW          462    .bss                data_transfer.o
    0x20000266   COMPRESSED   0x00000002   PAD
    0x20000268        -       0x00000018   Zero   RW         1029    .bss                bmi088.o
    0x20000280        -       0x00000060   Zero   RW         1211    .bss                c_w.l(libspace.o)
    0x200002e0        -       0x00002000   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x200022e0        -       0x00002000   Zero   RW            1    STACK               startup_stm32f407xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       168         12          0          4          0        843   beep.o
       648         70          0         36         24       2139   bmi088.o
      1088         38          0          0          0       3557   can.o
        68         14          0          5          0        826   control.o
       328         12          0          0         32       2277   data_transfer.o
      1144         68          0          2         58       4313   dbus_dr16.o
       180         12          0          4          0       1848   delay.o
       160         14          0         92          0       3490   dji_can_motor.o
        72         16          0          0          0        475   led.o
      1072         42          0         20          0       2966   mahonyahrs.o
      5416        108          0        344          0     245117   motor_move.o
       696         30          0          0          0       1224   pwm_motor.o
       260         24          0          0          0       1117   spi.o
        72         28        392          0      16384        872   startup_stm32f407xx.o
       834         44          0          0          0       4990   sys.o
      2384         86          0          8          0       1830   test.o
       308         22          0          8          0       1464   timer.o
       260         22          0          0          0       1248   usart.o

    ----------------------------------------------------------------------
     15160        <USER>        <GROUP>        524      16500     280596   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          0          1          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        22          0          0          0          0        100   _rserrno.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        10          0          0          0          0        116   fpinit.o
         0          0          0          0          0          0   usenofp.o
       300         42          0          0          0        176   asinf.o
       592         90          0          0          0        184   atan2f.o
        38          0          0          0          0        116   fpclassifyf.o
        44         12          0          0          0        464   funder.o
        62          0          0          0          0        136   sqrtf.o

    ----------------------------------------------------------------------
      1416        <USER>          <GROUP>          0         96       1944   Library Totals
        10          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       360         16          0          0         96        752   c_w.l
        10          0          0          0          0        116   fz_wm.l
      1036        144          0          0          0       1076   m_wm.l

    ----------------------------------------------------------------------
      1416        <USER>          <GROUP>          0         96       1944   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     16576        822        424        524      16596     276280   Grand Totals
     16576        822        424        268      16596     276280   ELF Image Totals (compressed)
     16576        822        424        268          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                17000 (  16.60kB)
    Total RW  Size (RW Data + ZI Data)             17120 (  16.72kB)
    Total ROM Size (Code + RO Data + RW Data)      17268 (  16.86kB)

==============================================================================

