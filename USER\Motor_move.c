#include "Motor_move.h"
#include "DJI_CAN_Motor.h"

#define Speed 800

int i;
int temp;
//int Delay;
//           1    2    3    4     5    6  7     8    9   10   11    12
int w[12]={2035,575 ,2060,520 ,2010,490 ,2170,730 ,2020,580 ,1995,400 };
int s[12]={1535,1075,1560,1020,1510,990 ,1670,1230,1520,1080,1495,900 };
int r[12]={1335,1275,1360,1220,1310,1190,1470,1430,1320,1280,1295,1100};
int z[12]={1035,1575,1060,1520,1010,1490,1170,1730,1020,1580,995,1400};
int n[12]={635 ,1975,660 ,1870,610 ,1890,770 ,2130,620 ,1980,595 ,1850};
int m[12]={1785,825 ,1810,770 ,1760,740 ,1920,980 ,1770,830 ,1745,650 };
int k[12]={1885,725 ,1910,670 ,1860,640 ,2020,880 ,1870,730 ,1845,550 };


void Motor_move(int Begin_1[],int End_1[],int Begin_2[],int End_2[],int Begin_3[],int End_3[],int Begin_4[],int End_4[],int Delay,int Steps)
//控制六只脚，十二只舵机
{
	for(i=0;i<=Steps;i++)
		{
			//左脚
			Can_Sevo_Control(3,Begin_1[2]+(End_1[2]-Begin_1[2])*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
			Can_Sevo_Control(4,Begin_2[3]+(End_2[3]-Begin_2[3])*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
			Can_Sevo_Control(6,Begin_1[5]+(End_1[5]-Begin_1[5])*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
			Can_Sevo_Control(5,Begin_2[4]+(End_2[4]-Begin_2[4])*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
			Can_Sevo_Control(12,Begin_1[11]+(End_1[11]-Begin_1[11])*i/Steps,Speed);
			delay_ms(Delay/Steps/12);			
			Can_Sevo_Control(11,Begin_2[10]+(End_2[10]-Begin_2[10])*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
			//右脚
			Can_Sevo_Control(1,Begin_3[0]+(End_3[0]-Begin_3[0])*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
		    Can_Sevo_Control(2,Begin_4[1]+(End_4[1]-Begin_4[1])*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
			Can_Sevo_Control(7,Begin_3[6]+(End_3[6]-Begin_3[6])*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
		    Can_Sevo_Control(8,Begin_4[7]+(End_4[7]-Begin_4[7])*i/Steps,Speed);
			delay_ms(Delay/Steps/12);	
			Can_Sevo_Control(10,Begin_3[9]+(End_3[9]-Begin_3[9])*i/Steps,Speed);
			delay_ms(Delay/Steps/12);	
			Can_Sevo_Control(9,Begin_4[8]+(End_4[8]-Begin_4[8])*i/Steps,Speed);
			delay_ms(Delay/Steps/12);	
		}
}

void Motor_walk2(int ID_1,int ID_2,int ID_3,int ID_4,int ID_5,int ID_6,int Begin_1,int End_1,int Begin_2,int End_2,int Begin_3,int End_3,int Begin_4,int End_4,int Begin_5,int End_5,
	int Begin_6,int End_6,
int ID_7,int ID_8,int ID_9,int ID_10,int ID_11,int ID_12,int Begin_7,int End_7,int Begin_8,int End_8,int Begin_9,int End_9,int Begin_10,int End_10,int Begin_11,int End_11,
	int Begin_12,int End_12,int Delay,int Steps)
{
		Can_Sevo_Control(ID_1,Begin_1,Speed);
		Can_Sevo_Control(ID_2,Begin_2,Speed);
		Can_Sevo_Control(ID_3,Begin_3,Speed);
		Can_Sevo_Control(ID_4,Begin_4,Speed);
		Can_Sevo_Control(ID_5,Begin_5,Speed);
		Can_Sevo_Control(ID_6,Begin_6,Speed);
		Can_Sevo_Control(ID_7,Begin_7,Speed);
		Can_Sevo_Control(ID_8,Begin_8,Speed);
		Can_Sevo_Control(ID_9,Begin_9,Speed);
		Can_Sevo_Control(ID_10,Begin_10,Speed);
		Can_Sevo_Control(ID_11,Begin_11,Speed);
		Can_Sevo_Control(ID_12,Begin_12,Speed);

	
	for(i=0;i<=Steps;i++)  //运动
		{
			Can_Sevo_Control(ID_1,Begin_1+(End_1-Begin_1)*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
		    Can_Sevo_Control(ID_2,Begin_2+(End_2-Begin_2)*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
			Can_Sevo_Control(ID_3,Begin_3+(End_3-Begin_3)*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
			Can_Sevo_Control(ID_4,Begin_4+(End_4-Begin_4)*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
			Can_Sevo_Control(ID_5,Begin_5+(End_5-Begin_5)*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
			Can_Sevo_Control(ID_6,Begin_6+(End_6-Begin_6)*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
			Can_Sevo_Control(ID_7,Begin_7+(End_7-Begin_7)*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
		    Can_Sevo_Control(ID_8,Begin_8+(End_8-Begin_8)*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
			Can_Sevo_Control(ID_9,Begin_9+(End_9-Begin_9)*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
			Can_Sevo_Control(ID_10,Begin_10+(End_10-Begin_10)*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
			Can_Sevo_Control(ID_11,Begin_11+(End_11-Begin_11)*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
			Can_Sevo_Control(ID_12,Begin_12+(End_12-Begin_12)*i/Steps,Speed);
			delay_ms(Delay/Steps/12);
		}
}

void Motor_stand(int Delay,int Steps)  //站立
{
		Motor_move(z,z,z,z,z,z,z,z,Delay,Steps);
}

void Motor_stand1(int Delay,int Steps)  //站立
{
		Motor_move(s,s,s,s,s,s,s,s,Delay,Steps);
}

void Motor_ctrl(int Delay,int Steps)  //蹲下
{
		Motor_move(r,r,r,r,r,r,r,r,Delay,Steps);
}


void Motor_Walk(int Delay,int Steps)  //行走
{
        Motor_move(n,s,s,s,s,z,n,z,Delay,Steps);//缩  中
        Motor_move(s,s,s,n,z,n,z,s,Delay,Steps);//前  后
        Motor_move(s,z,n,z,n,s,s,s,Delay,Steps);//中  缩
        Motor_move(z,n,z,s,s,s,s,n,Delay,Steps);//后  前
}

void Motor_Walk3(int Delay,int Steps)  //行走
{
        Motor_move(n,s,s,s,w,z,n,z,Delay,Steps);//缩  中
        Motor_move(s,w,s,n,z,n,z,s,Delay,Steps);//前  后
        Motor_move(w,z,n,z,n,s,s,s,Delay,Steps);//中  缩
        Motor_move(z,n,z,s,s,w,s,n,Delay,Steps);//后  前
}

void Motor_Left2(int Delay,int Steps)
{
		Motor_walk2(3,4,6,5,12,11,n_3,s_3,s_4,s_4,n_6,s_6,s_5,s_5,n_12,s_12,s_11,s_11,  //
		1,2,7,8,10,9,s_1,z_1,n_2,z_2,s_7,z_7,n_8,z_8,z_10-150,z_10-150,z_9+150,z_9+150,Delay,Steps);
		
		Motor_walk2(3,4,6,5,12,11,s_3,s_3,s_4,n_4,s_6,s_6,s_5,n_5,s_12,s_12,s_11,n_11,
		1,2,7,8,10,9,z_1,n_1,z_2,s_2,z_7,n_7,z_8,s_8,z_10-150,z_10-150,z_9+150,z_9+150,Delay,Steps);
	
		Motor_walk2(3,4,6,5,12,11,s_3,z_3,n_4,z_4,s_6,z_6,n_5,z_5,s_12,z_12,n_11,z_11,
		1,2,7,8,10,9,n_1,s_1,s_2,s_2,n_7,s_7,s_8,s_8,z_10-150,z_10-150,z_9+150,z_9+150,Delay,Steps);
	
		Motor_walk2(3,4,6,5,12,11,z_3,n_3,z_4,s_4,z_6,n_6,z_5,s_5,z_12,n_12,z_11,s_11,
		1,2,7,8,10,9,s_1,s_1,s_2,n_2,s_7,s_7,s_8,n_8,z_10-150,z_10-150,z_9+150,z_9+150,Delay,Steps);
}

void Motor_Right2(int Delay,int Steps)
{
		Motor_walk2(3,4,6,5,12,11,z_3+150,z_3+150,z_4-150,z_4-150,n_6,s_6,s_5,s_5,n_12,s_12,s_11,s_11,  //
		1,2,7,8,10,9,s_1,z_1,n_2,z_2,s_7,z_7,n_8,z_8,s_10,z_10,n_9,z_9,Delay,Steps);
		
		Motor_walk2(3,4,6,5,12,11,z_3+150,z_3+150,z_4-150,z_4-150,s_6,s_6,s_5,n_5,s_12,s_12,s_11,n_11,
		1,2,7,8,10,9,z_1,n_1,z_2,s_2,z_7,n_7,z_8,s_8,z_10,n_10,z_9,s_9,Delay,Steps);
	
		Motor_walk2(3,4,6,5,12,11,z_3+150,z_3+150,z_4-150,z_4-150,s_6,z_6,n_5,z_5,s_12,z_12,n_11,z_11,
		1,2,7,8,10,9,n_1,s_1,s_2,s_2,n_7,s_7,s_8,s_8,n_10,s_10,s_9,s_9,Delay,Steps);
	
		Motor_walk2(3,4,6,5,12,11,z_3+150,z_3+150,z_4-150,z_4-150,z_6,n_6,z_5,s_5,z_12,n_12,z_11,s_11,
		1,2,7,8,10,9,s_1,s_1,s_2,n_2,s_7,s_7,s_8,n_8,s_10,s_10,s_9,n_9,Delay,Steps);
}

void Motor_Walka(int Delay,int Steps)  //行走
{
        Motor_move(n,s,s,n,s,n,n,s,Delay,Steps);//前  后
        Motor_move(s,z,n,z,n,s,s,s,Delay,Steps);//中  缩
        Motor_move(z,n,z,s,s,s,s,n,Delay,Steps);//后  前
	
}

void Motor_Walk1(int Delay,int Steps)  //行走
{
		Motor_move(s,k,k,k,k,s,s,s,Delay,Steps);//缩  中
		Motor_move(k,k,k,s,s,s,s,k,Delay,Steps);//前  后
		Motor_move(k,s,s,s,s,k,k,k,Delay,Steps);//中  缩
		Motor_move(s,s,s,k,k,k,k,s,Delay,Steps);//后  前
}

void Motor_Walk2(int Delay,int Steps)  //行走
{
		Motor_move(m,k,w,k,w,m,m,m,Delay,Steps);//缩  中
		Motor_move(k,w,k,m,m,m,m,w,Delay,Steps);//前  后
		Motor_move(w,m,m,m,m,k,w,k,Delay,Steps);//中  缩
		Motor_move(m,m,m,w,k,w,k,m,Delay,Steps);//后  前
}

void Motor_Walk4(int Delay,int Steps)  //行走
{
		Motor_move(n,s,s,s,s,r,n,r,Delay,Steps);//缩  中
		Motor_move(s,s,s,n,r,n,r,s,Delay,Steps);//前  后
		Motor_move(s,r,n,r,n,s,s,s,Delay,Steps);//中  缩
		Motor_move(r,n,r,s,s,s,s,n,Delay,Steps);//后  前
}

void Motor_Back(int Delay,int Steps)  //后退
{
	Motor_move(s,s,n,s,n,z,s,z,Delay,Steps);//缩，中
	Motor_move(s,n,s,s,z,s,z,n,Delay,Steps);//后，前
	Motor_move(n,z,s,z,s,s,n,s,Delay,Steps);//中，缩
	Motor_move(z,s,z,n,s,n,s,s,Delay,Steps);//前，后
}

void Motor_Back1(int Delay,int Steps)  //后退
{
		Motor_move(k,k,s,k,s,s,k,s,Delay,Steps);//缩  中
		Motor_move(k,s,k,k,s,k,s,s,Delay,Steps);//后  前
		Motor_move(s,s,k,s,k,k,s,k,Delay,Steps);//中  缩
		Motor_move(s,k,s,s,k,s,k,k,Delay,Steps);//前  后
}

void Motor_Back2(int Delay,int Steps)  //后退
{
	Motor_move(m,k,w,k,w,m,m,m,Delay,Steps);//缩  中
	Motor_move(m,m,m,w,k,w,k,m,Delay,Steps);//后  前
	Motor_move(m,k,w,k,w,m,m,m,Delay,Steps);//中  缩
	Motor_move(k,m,k,w,m,w,m,m,Delay,Steps);//前  后
}

void Motor_Back4(int Delay,int Steps)  //后退
{
	Motor_move(s,s,n,s,n,r,s,r,Delay,Steps);//缩，中
	Motor_move(s,n,s,s,r,s,r,n,Delay,Steps);//后，前
	Motor_move(n,r,s,r,s,s,n,s,Delay,Steps);//中，缩
	Motor_move(r,s,r,n,s,n,s,s,Delay,Steps);//前，后
}

void Motor_Right(int Delay,int Steps)
{
        Motor_walk2(3,4,6,5,12,11,z_3,s_3,z_4,n_4,z_6,s_6,z_5,n_5,z_12,s_12,z_11,n_11,  //
        1,2,7,8,10,9,z_1,z_1,z_2,z_2,z_7,z_7,z_8,z_8,z_10,z_10,z_9,z_9,Delay,Steps);
        
        Motor_walk2(3,4,6,5,12,11,z_3,z_3,z_4,z_4,z_6,z_6,z_5,z_5,z_12,z_12,z_11,z_11,
        1,2,7,8,10,9,z_1,s_1,z_2,n_2,z_7,s_7,z_8,n_8,z_10,z_10,z_9,z_9,Delay,Steps);
}

void Motor_Right1(int Delay,int Steps)
{
		Motor_walk2(3,4,6,5,12,11,r_3,w_3,r_4,n_4,r_6,w_6,r_5,n_5,r_12,w_12,r_11,n_11,  //
		1,2,7,8,10,9,r_1,r_1,r_2,r_2,r_7,r_7,r_8,r_8,s_10,s_10,s_9,s_9,Delay,Steps);
		
		Motor_walk2(3,4,6,5,12,11,r_3,r_3,r_4,r_4,r_6,r_6,r_5,r_5,r_12,r_12,r_11,r_11,
		1,2,7,8,10,9,r_1,w_1,r_2,n_2,r_7,w_7,r_8,n_8,r_10,r_10,r_9,r_9,Delay,Steps);
}

void Motor_Left(int Delay,int Steps)
{
		Motor_walk2(3,4,6,5,12,11,z_3,z_3,z_4,z_4,z_6,z_6,z_5,z_5,z_12,z_12,z_11,z_11, //中 外
		1,2,7,8,10,9,z_1,s_1,z_2,n_2,z_7,s_7,z_8,n_8,z_10,s_10,z_9,n_9,Delay,Steps);
		
		Motor_walk2(3,4,6,5,12,11,z_3,z_3,z_4,z_4,z_6,s_6,z_5,n_5,z_12,s_12,z_11,n_11,//外 中
		1,2,7,8,10,9,z_1,z_1,z_2,z_2,z_7,z_7,z_8,z_8,z_10,z_10,z_9,z_9,Delay,Steps);
}

void Motor_Left1(int Delay,int Steps)
{
		Motor_walk2(3,4,6,5,12,11,r_3,r_3,r_4,r_4,r_6,r_6,r_5,r_5,r_12,r_12,r_11,r_11,  
		1,2,7,8,10,9,r_1,w_1,r_2,n_2,r_7,w_7,r_8,n_8,r_10,w_10,r_9,n_9,Delay,Steps);
		
		Motor_walk2(3,4,6,5,12,11,r_3,r_3,r_4,r_4,r_6,w_6,r_5,n_5,r_12,w_12,r_11,n_11,
		1,2,7,8,10,9,r_1,r_1,r_2,r_2,r_7,r_7,r_8,r_8,r_10,r_10,r_9,r_9,Delay,Steps);
}

void Motor_YD(int Delay,int Steps)
{
	Motor_move(n,s,s,s,s,n,s,s,Delay,Steps);//缩  后
	Motor_move(s,s,s,n,n,s,s,s,Delay,Steps);//前  缩
	Motor_move(s,s,n,s,s,s,s,n,Delay,Steps);//缩  前
	Motor_move(s,n,s,s,s,s,n,s,Delay,Steps);//后  缩
//	    Motor_walk2(3,4,6,5,12,11,n_3,s_3,s_4,s_4,n_6,s_6,s_5,s_5,n_12,s_12,s_11,s_11,  
//        1,2,7,8,10,9,s_1,n_1,s_2,s_2,s_7,n_7,s_8,s_8,s_10,n_10,s_9,s_9);  //缩  后 

//        Motor_walk2(3,4,6,5,12,11,s_3,s_3,s_4,n_4,s_6,s_6,s_5,n_5,s_12,s_12,s_11,n_11,  
//        1,2,7,8,10,9,n_1,s_1,s_2,s_2,n_7,s_7,s_8,s_8,n_10,s_10,s_9,s_9);  //前  缩
//    
//        Motor_walk2(3,4,6,5,12,11,s_3,s_3,n_4,s_4,s_6,s_6,n_5,s_5,s_12,s_12,n_11,s_11,
//        1,2,7,8,10,9,s_1,s_1,s_2,n_2,s_7,s_7,s_8,n_8,s_10,s_10,s_9,n_9);//缩   前
//    
//        Motor_walk2(3,4,6,5,12,11,s_3,n_3,s_4,s_4,s_6,n_6,s_5,s_5,s_12,n_12,s_11,s_11,
//        1,2,7,8,10,9,s_1,s_1,n_2,s_2,s_7,s_7,n_8,s_8,s_10,s_10,n_9,s_9);//后  缩
}
