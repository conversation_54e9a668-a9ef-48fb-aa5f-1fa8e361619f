
#include "sys.h"
#include "delay.h"   
#include "key.h"
#include "timer.h"
#include "usart.h"	
#include "led.h" 
#include "can.h"
#include "DJI_CAN_Motor.h"

u8 can_key;
u8 CAN_SendBuf[8]={0x05,0x00,0x01,0x00,0x01,0x00,0x01,0x00};
u8 CAN_RecvBuf[8];
u16 CAN_timeout;
u8 mbox;

u8 CAN_CarMotor_OffLine[4]={0,0,0,0};//底盘电机离线判断变量，会在接收到电机CAN数据时，给倒计时时间，在系统中断中减计时，倒计时清理，证明电机离线了
u8 CAN_CloudMotor_OffLine[2]={0,0};	//云台电机离线判断变量，会在接收到电机CAN数据时，给倒计时时间，在系统中断中减计时，倒计时清理，证明电机离线了
u8 CAN_ShootMotor_OffLine[2]={0,0};	//射击电机离线判断变量，会在接收到电机CAN数据时，给倒计时时间，在系统中断中减计时，倒计时清理，证明电机离线了

//short int CarMotorSetCurrent[4]={0,0,0,0};//底盘电机设置电流
short int CarMotorNowCurrent[4]={0,0,0,0};//底盘电机当前电流
short int CarMotorSetSpeed[4]={0,0,0,0};	//底盘电机设置速度
short int CarMotorNowSpeed[4]={0,0,0,0};	//底盘电机当前速度

//short int ShootMotorSetCurrent[3]={0,0,0};//射击电机设置电流
short int ShootMotorNowCurrent[3]={0,0,0};//射击电机当前电流
short int ShootMotorSetSpeed[3]={0,0,0};	//射击电机设置速度
short int ShootMotorNowSpeed[3]={0,0,0};	//射击电机当前速度

//short int CloudMotorSetCurrent[2]={0,0};//云台电机设置电流
short int CloudMotorNowCurrent[2]={0,0};//云台电机当前电流
short int CloudMotorSetSpeed[2]={0,0};	//云台电机设置速度
short int CloudMotorNowSpeed[2]={0,0};	//云台电机当前速度
short int CloudMotorSetAngle[2]={0,0};	//云台电机设置角度
short int CloudMotorNowAngle[2]={0,0};	//云台电机当前角度

void CAN1_Motor_Init(void)
{	
	CAN1_Mode_Init(1,6,7,3,0);	//CAN初始化,波特率1000Kbps  		
}
/*
//控制电机转速
//电机电流控制范围：-16384~16384
void Set_CAN1_Motor(u16 can_id,int MotorA,int MotorB,int MotorC,int MotorD)
{
	if(MotorA<MOTOR_CURREN_MIN)MotorA=MOTOR_CURREN_MIN;if(MotorA>MOTOR_CURREN_MAX)MotorA=MOTOR_CURREN_MAX;
	if(MotorB<MOTOR_CURREN_MIN)MotorB=MOTOR_CURREN_MIN;if(MotorB>MOTOR_CURREN_MAX)MotorB=MOTOR_CURREN_MAX;
	if(MotorC<MOTOR_CURREN_MIN)MotorC=MOTOR_CURREN_MIN;if(MotorC>MOTOR_CURREN_MAX)MotorC=MOTOR_CURREN_MAX;
	if(MotorD<MOTOR_CURREN_MIN)MotorD=MOTOR_CURREN_MIN;if(MotorD>MOTOR_CURREN_MAX)MotorD=MOTOR_CURREN_MAX;
	
	CAN_SendBuf[0]=(MotorA>>8)&0xFF;CAN_SendBuf[1]=MotorA&0xFF;
	CAN_SendBuf[2]=(MotorB>>8)&0xFF;CAN_SendBuf[3]=MotorB&0xFF;
	CAN_SendBuf[4]=(MotorC>>8)&0xFF;CAN_SendBuf[5]=MotorC&0xFF;
	CAN_SendBuf[6]=(MotorD>>8)&0xFF;CAN_SendBuf[7]=MotorD&0xFF;
	while((CAN1_Tx_Staus(mbox)!=0X07)&&(CAN_timeout<0XFFF))CAN_timeout++;//等待发送结束
	mbox=CAN1_Tx_Msg(can_id,0,0,8,CAN_SendBuf);CAN_timeout=0;
		
}

void Set_CAN1_CloudMotor(u16 can_id,int MotorA,int MotorB,int MotorC,int MotorD)
{
		
	CAN_SendBuf[0]=(MotorA>>8)&0xFF;CAN_SendBuf[1]=MotorA&0xFF;
	CAN_SendBuf[2]=(MotorB>>8)&0xFF;CAN_SendBuf[3]=MotorB&0xFF;
	CAN_SendBuf[4]=(MotorC>>8)&0xFF;CAN_SendBuf[5]=MotorC&0xFF;
	CAN_SendBuf[6]=(MotorD>>8)&0xFF;CAN_SendBuf[7]=MotorD&0xFF;
	while((CAN1_Tx_Staus(mbox)!=0X07)&&(CAN_timeout<0XFFF))CAN_timeout++;//等待发送结束
	mbox=CAN1_Tx_Msg(can_id,0,0,8,CAN_SendBuf);CAN_timeout=0;
	
}
*/


//角度增量旋转
void Can_Sevo_Control(u16 can_id,int Angle,int Speed)
{
//		Angle=-200;
//		Speed=1000;	
		CAN_SendBuf[0]=0x03;
		CAN_SendBuf[1]=Angle&0xFF;
		CAN_SendBuf[2]=(Angle>>8)&0xFF;
		CAN_SendBuf[3]=Speed&0xFF;
		CAN_SendBuf[4]=(Speed>>8)&0xFF;
		CAN_SendBuf[5]=0;
		CAN_SendBuf[6]=0;
		CAN_SendBuf[7]=0;
		while((CAN1_Tx_Staus(mbox)!=0X07)&&(CAN_timeout<0XFFF))CAN_timeout++;//等待发送结束
		mbox=CAN1_Tx_Msg(can_id,0,0,8,CAN_SendBuf);CAN_timeout=0;
}	


//中断服务函数			    
void CAN1_RX0_IRQHandler(void)
{
	u8 rxbuf[8];
	u32 id;
	u8 ide,rtr,len;
	u8 n;
	short int tempx;
 	CAN1_Rx_Msg(0,&id,&ide,&rtr,&len,rxbuf);
	/*
	switch(id)
	{
/////////////////////底盘电机/////////////////////////////////
		case 0x201://C620电调，ID：1
			tempx=rxbuf[4];tempx<<=8;tempx|=rxbuf[5];
			CarMotorNowCurrent[0]=tempx;//解电流数据
			tempx=rxbuf[2];tempx<<=8;tempx|=rxbuf[3];
			CarMotorNowSpeed[0]=tempx;//解速度数据		
			CAN_CarMotor_OffLine[0]=100;//设置100ms倒计时，倒计时清理，证明电机离线了		
			break;
		case 0x202://C620电调，ID：2
			tempx=rxbuf[4];tempx<<=8;tempx|=rxbuf[5];
			CarMotorNowCurrent[1]=tempx;//解电流数据
			tempx=rxbuf[2];tempx<<=8;tempx|=rxbuf[3];
			CarMotorNowSpeed[1]=tempx;//解速度数据	
			CAN_CarMotor_OffLine[1]=100;//设置100ms倒计时，倒计时清理，证明电机离线了			
			break;
		case 0x203://C620电调，ID：3
			tempx=rxbuf[4];tempx<<=8;tempx|=rxbuf[5];
			CarMotorNowCurrent[2]=tempx;//解电流数据
			tempx=rxbuf[2];tempx<<=8;tempx|=rxbuf[3];
			CarMotorNowSpeed[2]=tempx;//解速度数据	
			CAN_CarMotor_OffLine[2]=100;//设置100ms倒计时，倒计时清理，证明电机离线了			
			break;
		case 0x204://C620电调，ID：4
			tempx=rxbuf[4];tempx<<=8;tempx|=rxbuf[5];
			CarMotorNowCurrent[3]=tempx;//解电流数据
			tempx=rxbuf[2];tempx<<=8;tempx|=rxbuf[3];
			CarMotorNowSpeed[3]=tempx;//解速度数据	
			CAN_CarMotor_OffLine[3]=100;//设置100ms倒计时，倒计时清理，证明电机离线了			
			break;
/////////////////////射击电机/////////////////////////////////		
		case 0x207://C610电调，ID：7
			tempx=rxbuf[4];tempx<<=8;tempx|=rxbuf[5];
			ShootMotorNowCurrent[0]=tempx;//解电流数据
			tempx=rxbuf[2];tempx<<=8;tempx|=rxbuf[3];
			ShootMotorNowSpeed[0]=tempx;//解速度数据
			CAN_CloudMotor_OffLine[0]=100;//设置100ms倒计时，倒计时清理，证明电机离线了			
			break;
/////////////////////云台电机/////////////////////////////////				
		case 0x205://GM6020电机，ID：1
			tempx=rxbuf[4];tempx<<=8;tempx|=rxbuf[5];
			CloudMotorNowCurrent[0]=tempx;//解电流数据
			tempx=rxbuf[2];tempx<<=8;tempx|=rxbuf[3];
			CloudMotorNowSpeed[0]=tempx;//解速度数据	
			tempx=rxbuf[0];tempx<<=8;tempx|=rxbuf[1];
			CloudMotorNowAngle[0]=tempx;//解角度数据
			CAN_ShootMotor_OffLine[0]=100;//设置100ms倒计时，倒计时清理，证明电机离线了		
			break;
		case 0x206://GM6020电机，ID：2
			tempx=rxbuf[4];tempx<<=8;tempx|=rxbuf[5];
			CloudMotorNowCurrent[1]=tempx;//解电流数据
			tempx=rxbuf[2];tempx<<=8;tempx|=rxbuf[3];
			CloudMotorNowSpeed[1]=tempx;//解速度数据	
			tempx=rxbuf[0];tempx<<=8;tempx|=rxbuf[1];
			CloudMotorNowAngle[1]=tempx;//解角度数据
			CAN_ShootMotor_OffLine[1]=100;//设置100ms倒计时，倒计时清理，证明电机离线了
			break;		
		default:break;
	}*/
}



/*************************************END************************************/

