#ifndef __Motor_move_H__
#define __Motor_move_H__

#define w_1    2035
#define s_1    1535
#define z_1    1035
#define n_1    635
#define r_1    1335
#define m_1    1785

#define w_2    575
#define s_2    1075
#define z_2    1575
#define n_2    1975
#define r_2    1275

#define w_3    2070
#define s_3    1570
#define z_3    1070
#define n_3    670
#define r_3    1370

#define w_4    520
#define s_4    1020
#define z_4    1520
#define n_4    1920
#define r_4    1220

#define w_5    2010
#define s_5    1510
#define z_5    1010
#define n_5    610
#define r_5    1310

#define w_6    490
#define s_6    990
#define z_6    1490
#define n_6    1890
#define r_6    1190

#define w_7    2170
#define s_7    1670
#define z_7    1170
#define n_7    770
#define r_7    1470

#define w_8    730
#define s_8    1230
#define z_8    1730
#define n_8    2130
#define r_8    1430

#define w_9    2020
#define s_9    1520
#define z_9    1020
#define n_9    620
#define r_9    1300

#define w_10    580
#define s_10    1080
#define z_10    1580
#define n_10    1980
#define r_10    1280

#define w_11    1995
#define s_11    1495
#define z_11    995
#define n_11    595
#define r_11    1295

#define w_12    400
#define s_12    900
#define z_12    1400
#define n_12    1850
#define r_12    1100

void Motor_move(int Begin_1[],int End_1[],int Begin_2[],int End_2[],int Begin_3[],int End_3[],int Begin_4[],int End_4[],int Delay,int Steps);

void Motor_stand(int Delay,int Steps);

void Motor_stand1(int Delay,int Steps);

void Motor_ctrl(int Delay,int Steps);

void Motor_Walk(int Delay,int Steps);

void Motor_Walka(int Delay,int Steps);

void Motor_Walk1(int Delay,int Steps);

void Motor_Walk2(int Delay,int Steps);

void Motor_Walk3(int Delay,int Steps);

void Motor_Walk4(int Delay,int Steps);

void Motor_Back(int Delay,int Steps);

void Motor_Back1(int Delay,int Steps);

void Motor_Back2(int Delay,int Steps);

void Motor_Back4(int Delay,int Steps);

void Motor_Right(int Delay,int Steps);

void Motor_Right1(int Delay,int Steps);

void Motor_Right2(int Delay,int Steps);

void Motor_Left(int Delay,int Steps);

void Motor_Left1(int Delay,int Steps);

void Motor_Left2(int Delay,int Steps);

void Motor_YD(int Delay,int Steps);

#endif
